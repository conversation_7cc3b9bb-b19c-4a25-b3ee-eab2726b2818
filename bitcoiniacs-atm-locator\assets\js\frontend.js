/**
 * Frontend JavaScript for Bitcoiniacs ATM Locator
 */

(function($) {
  'use strict';

  // Global object to manage multiple map instances
  window.BitcoiniacsATMLocator = {
    maps: {},
    markers: {},
    userMarkers: {},
    
    /**
     * Initialize a map instance
     */
    initMap: function(mapId, options) {
      const self = this;
      
      // Default options
      const defaults = {
        zoom: 4,
        center: [53.7267, -127.6476],
        province: '',
        country: '',
        service: '',
        mapStyle: 'dark'
      };
      
      options = $.extend(defaults, options);
      
      // Wait for Leaflet to load
      if (typeof L === 'undefined') {
        console.error('Leaflet not loaded');
        self.showMapError(mapId, 'Map library not loaded');
        return;
      }
      
      try {
        // Initialize map
        const map = L.map(mapId).setView(options.center, options.zoom);
        
        // Add tile layer based on style
        self.addTileLayer(map, options.mapStyle);
        
        // Store map instance
        self.maps[mapId] = map;
        self.markers[mapId] = [];
        
        // Load ATM locations
        self.loadATMLocations(mapId, options);
        
        // Set up event listeners
        self.setupEventListeners(mapId);
        
      } catch (error) {
        console.error('Map initialization error:', error);
        self.showMapError(mapId, error.message);
      }
    },
    
    /**
     * Add tile layer based on style
     */
    addTileLayer: function(map, style) {
      let tileUrl, attribution;
      
      switch (style) {
        case 'light':
          tileUrl = 'https://tiles.stadiamaps.com/tiles/alidade_smooth/{z}/{x}/{y}{r}.png';
          attribution = '&copy; <a href="https://stadiamaps.com/">Stadia Maps</a>';
          break;
        case 'satellite':
          tileUrl = 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';
          attribution = '&copy; <a href="https://www.esri.com/">Esri</a>';
          break;
        case 'dark':
        default:
          tileUrl = 'https://tiles.stadiamaps.com/tiles/alidade_smooth_dark/{z}/{x}/{y}{r}.png';
          attribution = '&copy; <a href="https://stadiamaps.com/">Stadia Maps</a>';
          break;
      }
      
      L.tileLayer(tileUrl, {
        attribution: attribution,
        maxZoom: 20,
      }).addTo(map);
    },
    
    /**
     * Load ATM locations
     */
    loadATMLocations: function(mapId, filters) {
      const self = this;
      const map = self.maps[mapId];
      
      if (!map) return;
      
      // Show loading state
      self.showLoading(mapId);
      
      // AJAX request to get locations
      $.ajax({
        url: bitcoiniacs_atm_ajax.ajax_url,
        type: 'POST',
        data: {
          action: 'bitcoiniacs_atm_get_locations',
          nonce: bitcoiniacs_atm_ajax.nonce,
          province: filters.province || '',
          country: filters.country || '',
          service: filters.service || '',
          search: filters.search || ''
        },
        success: function(response) {
          self.hideLoading(mapId);
          
          if (response.success) {
            self.addMarkersToMap(mapId, response.data);
          } else {
            self.showMapError(mapId, 'Failed to load ATM locations');
          }
        },
        error: function() {
          self.hideLoading(mapId);
          self.showMapError(mapId, 'Network error while loading ATM locations');
        }
      });
    },
    
    /**
     * Add markers to map
     */
    addMarkersToMap: function(mapId, locations) {
      const self = this;
      const map = self.maps[mapId];
      
      if (!map) return;
      
      // Clear existing markers
      self.clearMarkers(mapId);
      
      // Add new markers
      locations.forEach(function(location) {
        const coords = [location.latitude, location.longitude];
        
        const icon = L.divIcon({
          className: 'custom-marker',
          html: '🏧',
          iconSize: [30, 30],
          iconAnchor: [15, 15]
        });
        
        const marker = L.marker(coords, { icon }).addTo(map);
        
        // Create popup content
        const popupContent = self.createPopupContent(location);
        marker.bindPopup(popupContent);
        
        // Store marker data
        marker.atmData = location;
        self.markers[mapId].push(marker);
      });
    },
    
    /**
     * Create popup content for marker
     */
    createPopupContent: function(location) {
      let services = [];
      if (location.can_buy) services.push('<span class="feature-tag">💰 Buy</span>');
      if (location.can_sell) services.push('<span class="feature-tag">💸 Sell</span>');
      
      let content = `
        <div class="popup-title">${location.name}</div>
        <div class="popup-address">${location.address}</div>
      `;
      
      if (location.distance !== undefined) {
        content += `<div class="distance-info">📏 ${location.distance} km away</div>`;
      }
      
      if (services.length > 0) {
        content += `<div class="popup-features">${services.join('')}</div>`;
      }
      
      if (location.operating_hours) {
        content += `<div class="popup-hours"><strong>Hours:</strong> ${location.operating_hours}</div>`;
      }
      
      if (location.phone) {
        content += `<div class="popup-phone"><strong>Phone:</strong> ${location.phone}</div>`;
      }
      
      return content;
    },
    
    /**
     * Clear all markers from map
     */
    clearMarkers: function(mapId) {
      const self = this;
      const markers = self.markers[mapId] || [];
      
      markers.forEach(function(marker) {
        self.maps[mapId].removeLayer(marker);
      });
      
      self.markers[mapId] = [];
    },
    
    /**
     * Set up event listeners for controls
     */
    setupEventListeners: function(mapId) {
      const self = this;
      const container = $('#' + mapId + '-container');
      
      // Province filter
      container.find('.bitcoiniacs-province-filter').on('change', function() {
        self.filterMarkers(mapId);
      });
      
      // Service filter
      container.find('.bitcoiniacs-service-filter').on('change', function() {
        self.filterMarkers(mapId);
      });
      
      // Search input
      container.find('.bitcoiniacs-search-input').on('input', function() {
        clearTimeout(self.searchTimeout);
        self.searchTimeout = setTimeout(function() {
          self.filterMarkers(mapId);
        }, 500);
      });
      
      // Locate button
      container.find('.bitcoiniacs-btn-locate').on('click', function() {
        self.findNearestATMs(mapId);
      });
      
      // Reset button
      container.find('.bitcoiniacs-btn-reset').on('click', function() {
        self.resetView(mapId);
      });
    },
    
    /**
     * Filter markers based on current control values
     */
    filterMarkers: function(mapId) {
      const self = this;
      const container = $('#' + mapId + '-container');
      
      const filters = {
        province: container.find('.bitcoiniacs-province-filter').val(),
        service: container.find('.bitcoiniacs-service-filter').val(),
        search: container.find('.bitcoiniacs-search-input').val()
      };
      
      self.loadATMLocations(mapId, filters);
    },
    
    /**
     * Find nearest ATMs using geolocation
     */
    findNearestATMs: function(mapId) {
      const self = this;
      const map = self.maps[mapId];
      const button = $('#' + mapId + '-container').find('.bitcoiniacs-btn-locate');
      
      if (!navigator.geolocation) {
        alert('Geolocation is not supported by this browser.');
        return;
      }
      
      // Update button state
      const originalText = button.html();
      button.html('<div class="bitcoiniacs-spinner"></div>Getting your location...').prop('disabled', true);
      
      navigator.geolocation.getCurrentPosition(
        function(position) {
          const userLocation = [position.coords.latitude, position.coords.longitude];
          
          // Add user location marker
          if (self.userMarkers[mapId]) {
            map.removeLayer(self.userMarkers[mapId]);
          }
          
          self.userMarkers[mapId] = L.marker(userLocation, {
            icon: L.divIcon({
              className: 'user-marker',
              html: '📍',
              iconSize: [30, 30],
              iconAnchor: [15, 15]
            })
          }).addTo(map);
          
          self.userMarkers[mapId].bindPopup('📍 Your Location').openPopup();
          
          // Update markers with distances
          self.updateMarkersWithDistances(mapId, userLocation);
          
          // Find and zoom to closest ATM
          self.zoomToClosestATM(mapId, userLocation);
          
          // Restore button
          button.html(originalText).prop('disabled', false);
        },
        function(error) {
          let message = 'Unable to get your location. ';
          switch(error.code) {
            case error.PERMISSION_DENIED:
              message += 'Location access was denied.';
              break;
            case error.POSITION_UNAVAILABLE:
              message += 'Location information is unavailable.';
              break;
            case error.TIMEOUT:
              message += 'Location request timed out.';
              break;
          }
          alert(message);
          button.html(originalText).prop('disabled', false);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000
        }
      );
    },
    
    /**
     * Update markers with distance information
     */
    updateMarkersWithDistances: function(mapId, userLocation) {
      const self = this;
      const markers = self.markers[mapId] || [];
      
      markers.forEach(function(marker) {
        const distance = self.calculateDistance(
          userLocation[0], userLocation[1],
          marker.getLatLng().lat, marker.getLatLng().lng
        );
        
        marker.atmData.distance = distance.toFixed(1);
        
        // Update popup content
        const popupContent = self.createPopupContent(marker.atmData);
        marker.setPopupContent(popupContent);
      });
    },
    
    /**
     * Zoom to closest ATM
     */
    zoomToClosestATM: function(mapId, userLocation) {
      const self = this;
      const map = self.maps[mapId];
      const markers = self.markers[mapId] || [];
      
      let closestMarker = null;
      let closestDistance = Infinity;
      
      markers.forEach(function(marker) {
        const distance = self.calculateDistance(
          userLocation[0], userLocation[1],
          marker.getLatLng().lat, marker.getLatLng().lng
        );
        
        if (distance < closestDistance) {
          closestDistance = distance;
          closestMarker = marker;
        }
      });
      
      if (closestMarker) {
        map.setView(closestMarker.getLatLng(), 12);
        closestMarker.openPopup();
      }
    },
    
    /**
     * Reset map view
     */
    resetView: function(mapId) {
      const self = this;
      const map = self.maps[mapId];
      const container = $('#' + mapId + '-container');
      
      // Reset map view
      map.setView([53.7267, -127.6476], 4);
      
      // Clear user marker
      if (self.userMarkers[mapId]) {
        map.removeLayer(self.userMarkers[mapId]);
        delete self.userMarkers[mapId];
      }
      
      // Reset filters
      container.find('.bitcoiniacs-province-filter').val('all');
      container.find('.bitcoiniacs-service-filter').val('all');
      container.find('.bitcoiniacs-search-input').val('');
      
      // Reload markers
      self.loadATMLocations(mapId, {});
    },
    
    /**
     * Calculate distance between two points
     */
    calculateDistance: function(lat1, lng1, lat2, lng2) {
      const R = 6371; // Earth's radius in km
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLng = (lng2 - lng1) * Math.PI / 180;
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng/2) * Math.sin(dLng/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      return R * c;
    },
    
    /**
     * Show loading state
     */
    showLoading: function(mapId) {
      // Implementation depends on UI design
    },
    
    /**
     * Hide loading state
     */
    hideLoading: function(mapId) {
      // Implementation depends on UI design
    },
    
    /**
     * Show map error
     */
    showMapError: function(mapId, message) {
      const mapContainer = $('#' + mapId);
      mapContainer.html(`
        <div class="bitcoiniacs-error-message">
          <h3>⚠️ Map Loading Error</h3>
          <p>Unable to load the interactive map.</p>
          <p><small>Error: ${message}</small></p>
        </div>
      `);
    }
  };

  /**
   * Visual Location Selector Class
   */
  window.BitcoiniacsVisualSelector = function(mapId) {
    this.mapId = mapId;
    this.currentCountry = null;
    this.currentRegion = null;
    this.regions = {
      'CA': [
        { code: 'AB', name: 'Alberta', icon: '🏔️', center: [53.9333, -116.5765], zoom: 6 },
        { code: 'BC', name: 'British Columbia', icon: '🌲', center: [53.7267, -127.6476], zoom: 6 },
        { code: 'ON', name: 'Ontario', icon: '🏙️', center: [51.2538, -85.3232], zoom: 6 },
        { code: 'QC', name: 'Quebec', icon: '🍁', center: [52.9399, -73.5491], zoom: 6 },
        { code: 'NS', name: 'Nova Scotia', icon: '🌊', center: [44.6820, -63.7443], zoom: 7 },
        { code: 'NB', name: 'New Brunswick', icon: '🦞', center: [46.5653, -66.4619], zoom: 7 }
      ],
      'US': [
        { code: 'CA', name: 'California', icon: '☀️', center: [36.7783, -119.4179], zoom: 6 },
        { code: 'NY', name: 'New York', icon: '🗽', center: [40.7589, -73.9851], zoom: 7 },
        { code: 'TX', name: 'Texas', icon: '🤠', center: [31.9686, -99.9018], zoom: 6 },
        { code: 'FL', name: 'Florida', icon: '🌴', center: [27.7663, -81.6868], zoom: 6 }
      ],
      'PH': [
        { code: 'MNL', name: 'Metro Manila', icon: '🏙️', center: [14.5995, 120.9842], zoom: 10 },
        { code: 'CEB', name: 'Cebu', icon: '🏝️', center: [10.3157, 123.8854], zoom: 9 }
      ]
    };
  };

  BitcoiniacsVisualSelector.prototype = {

    /**
     * Initialize visual selector
     */
    init: function() {
      this.setupEventListeners();
      this.loadATMCounts();
    },

    /**
     * Setup event listeners
     */
    setupEventListeners: function() {
      const self = this;
      const container = $('#' + this.mapId + '-visual-selector');

      // Tab switching
      container.on('click', '.selector-tab', function() {
        const tab = $(this).data('tab');
        self.switchTab(tab);
      });

      // Country selection
      container.on('click', '.country-card', function() {
        const country = $(this).data('country');
        const center = $(this).data('center');
        const zoom = $(this).data('zoom');

        self.selectCountry(country, center, zoom);
      });

      // Region selection
      container.on('click', '.region-card', function() {
        const region = $(this).data('region');
        const center = $(this).data('center');
        const zoom = $(this).data('zoom');

        self.selectRegion(region, center, zoom);
      });

      // City selection
      container.on('click', '.city-card', function() {
        const city = $(this).data('city');
        const center = $(this).data('center');
        const zoom = $(this).data('zoom');

        self.selectCity(city, center, zoom);
      });

      // Breadcrumb navigation
      container.on('click', '.breadcrumb-link', function() {
        const tab = $(this).data('tab');
        self.switchTab(tab);
      });
    },

    /**
     * Switch between tabs
     */
    switchTab: function(tab) {
      const container = $('#' + this.mapId + '-visual-selector');

      // Update tab buttons
      container.find('.selector-tab').removeClass('active');
      container.find('.selector-tab[data-tab="' + tab + '"]').addClass('active');

      // Update panels
      container.find('.selector-panel').removeClass('active');
      container.find('#' + tab + '-panel').addClass('active');
    },

    /**
     * Select country
     */
    selectCountry: function(country, center, zoom) {
      this.currentCountry = country;

      // Update map view
      if (BitcoiniacsATMLocator.maps[this.mapId]) {
        BitcoiniacsATMLocator.maps[this.mapId].setView(center, zoom);
      }

      // Filter locations by country
      this.filterByCountry(country);

      // Load regions for this country
      this.loadRegions(country);

      // Switch to region tab
      this.switchTab('region');
    },

    /**
     * Select region
     */
    selectRegion: function(region, center, zoom) {
      this.currentRegion = region;

      // Update map view
      if (BitcoiniacsATMLocator.maps[this.mapId]) {
        BitcoiniacsATMLocator.maps[this.mapId].setView(center, zoom);
      }

      // Filter locations by region
      this.filterByRegion(region);

      // Load cities for this region
      this.loadCities(this.currentCountry, region);

      // Switch to city tab
      this.switchTab('city');
    },

    /**
     * Select city
     */
    selectCity: function(city, center, zoom) {
      // Update map view
      if (BitcoiniacsATMLocator.maps[this.mapId]) {
        BitcoiniacsATMLocator.maps[this.mapId].setView(center, zoom);
      }

      // Filter locations by city
      this.filterByCity(city);
    },

    /**
     * Load ATM counts for countries
     */
    loadATMCounts: function() {
      const self = this;

      $.ajax({
        url: bitcoiniacs_atm_ajax.ajax_url,
        type: 'POST',
        data: {
          action: 'bitcoiniacs_atm_get_locations',
          nonce: bitcoiniacs_atm_ajax.nonce
        },
        success: function(response) {
          if (response.success) {
            self.updateATMCounts(response.data);
          }
        }
      });
    },

    /**
     * Update ATM counts in country cards
     */
    updateATMCounts: function(locations) {
      const counts = {};

      locations.forEach(function(location) {
        counts[location.country] = (counts[location.country] || 0) + 1;
      });

      $('.atm-count').each(function() {
        const country = $(this).data('country');
        $(this).text(counts[country] || 0);
      });
    },

    /**
     * Load regions for country
     */
    loadRegions: function(country) {
      const container = $('#region-grid');
      const regions = this.regions[country] || [];

      // Update breadcrumb
      $('.current-country').text(this.getCountryName(country));

      if (regions.length === 0) {
        container.html('<p class="no-regions">No regions available for this country.</p>');
        return;
      }

      let html = '';
      regions.forEach(function(region) {
        html += `
          <div class="region-card" data-region="${region.code}" data-center="[${region.center[0]}, ${region.center[1]}]" data-zoom="${region.zoom}">
            <span class="region-icon">${region.icon}</span>
            <h4>${region.name}</h4>
            <p class="region-stats">
              <span class="atm-count" data-region="${region.code}">-</span> ATMs
            </p>
          </div>
        `;
      });

      container.html(html);

      // Load ATM counts for regions
      this.loadRegionATMCounts(country);
    },

    /**
     * Load cities for region
     */
    loadCities: function(country, region) {
      const self = this;
      const container = $('#city-grid');

      // Update breadcrumb
      $('.current-region').text(this.getRegionName(country, region));

      // Show loading
      container.html('<div class="selector-loading"><div class="bitcoiniacs-spinner"></div>Loading cities...</div>');

      // Get locations for this region to extract cities
      $.ajax({
        url: bitcoiniacs_atm_ajax.ajax_url,
        type: 'POST',
        data: {
          action: 'bitcoiniacs_atm_get_locations',
          nonce: bitcoiniacs_atm_ajax.nonce,
          country: country,
          province: region
        },
        success: function(response) {
          if (response.success) {
            self.renderCities(response.data);
          } else {
            container.html('<p class="no-cities">No cities found for this region.</p>');
          }
        },
        error: function() {
          container.html('<p class="no-cities">Error loading cities.</p>');
        }
      });
    },

    /**
     * Render cities from location data
     */
    renderCities: function(locations) {
      const container = $('#city-grid');
      const cities = {};

      // Extract unique cities from locations
      locations.forEach(function(location) {
        const cityMatch = location.address.match(/([^,]+),\s*[A-Z]{2}/);
        if (cityMatch) {
          const cityName = cityMatch[1].trim();
          if (!cities[cityName]) {
            cities[cityName] = {
              name: cityName,
              count: 0,
              latitude: location.latitude,
              longitude: location.longitude
            };
          }
          cities[cityName].count++;
        }
      });

      if (Object.keys(cities).length === 0) {
        container.html('<p class="no-cities">No cities found for this region.</p>');
        return;
      }

      let html = '';
      Object.values(cities).forEach(function(city) {
        html += `
          <div class="city-card" data-city="${city.name}" data-center="[${city.latitude}, ${city.longitude}]" data-zoom="12">
            <span class="city-icon">🏙️</span>
            <h4>${city.name}</h4>
            <p class="city-stats">
              <span class="atm-count">${city.count}</span> ATMs
            </p>
          </div>
        `;
      });

      container.html(html);
    },

    /**
     * Filter locations by country
     */
    filterByCountry: function(country) {
      const container = $('#' + this.mapId + '-container');
      container.find('.bitcoiniacs-province-filter').val('all');

      // Trigger filter update
      BitcoiniacsATMLocator.loadATMLocations(this.mapId, { country: country });
    },

    /**
     * Filter locations by region
     */
    filterByRegion: function(region) {
      const container = $('#' + this.mapId + '-container');
      container.find('.bitcoiniacs-province-filter').val(region);

      // Trigger filter update
      BitcoiniacsATMLocator.loadATMLocations(this.mapId, {
        country: this.currentCountry,
        province: region
      });
    },

    /**
     * Filter locations by city
     */
    filterByCity: function(city) {
      const container = $('#' + this.mapId + '-container');
      container.find('.bitcoiniacs-search-input').val(city);

      // Trigger filter update
      BitcoiniacsATMLocator.loadATMLocations(this.mapId, {
        country: this.currentCountry,
        province: this.currentRegion,
        search: city
      });
    },

    /**
     * Load ATM counts for regions
     */
    loadRegionATMCounts: function(country) {
      const self = this;

      $.ajax({
        url: bitcoiniacs_atm_ajax.ajax_url,
        type: 'POST',
        data: {
          action: 'bitcoiniacs_atm_get_locations',
          nonce: bitcoiniacs_atm_ajax.nonce,
          country: country
        },
        success: function(response) {
          if (response.success) {
            const counts = {};
            response.data.forEach(function(location) {
              counts[location.province] = (counts[location.province] || 0) + 1;
            });

            $('[data-region]').each(function() {
              const region = $(this).data('region');
              $(this).find('.atm-count').text(counts[region] || 0);
            });
          }
        }
      });
    },

    /**
     * Get country name
     */
    getCountryName: function(code) {
      const names = {
        'CA': 'Canada',
        'US': 'United States',
        'PH': 'Philippines'
      };
      return names[code] || code;
    },

    /**
     * Get region name
     */
    getRegionName: function(country, code) {
      const regions = this.regions[country] || [];
      const region = regions.find(r => r.code === code);
      return region ? region.name : code;
    }
  };

})(jQuery);
