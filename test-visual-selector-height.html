<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visual Selector Height Test</title>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.02);
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #4ecdc4;
            border-bottom: 1px solid rgba(78, 205, 196, 0.3);
            padding-bottom: 10px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .comparison-item {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 10px;
            padding: 15px;
        }
        
        .comparison-item h3 {
            margin-top: 0;
            color: #96ceb4;
        }
        
        .height-info {
            background: rgba(78, 205, 196, 0.1);
            border: 1px solid rgba(78, 205, 196, 0.3);
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            font-size: 0.9rem;
        }
        
        .responsive-test {
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .responsive-test iframe {
            width: 100%;
            border: none;
            background: white;
        }
        
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
        }

        /* Visual Selector Styles - Updated with height fixes */
        .bitcoiniacs-visual-selector {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .visual-selector-header {
            text-align: center;
            margin-bottom: 15px;
        }

        .visual-selector-header h3 {
            margin: 0 0 3px 0;
            color: #4ecdc4;
            font-size: 1.3rem;
        }

        .visual-selector-header p {
            margin: 0;
            color: #b0b0b0;
            font-size: 0.85rem;
        }

        .visual-selector-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .selector-tab {
            background: none;
            border: none;
            color: #b0b0b0;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
            font-size: 0.9rem;
        }

        .selector-tab.active,
        .selector-tab:hover {
            color: #4ecdc4;
            border-bottom-color: #4ecdc4;
        }

        .selector-panel {
            display: none;
        }

        .selector-panel.active {
            display: block;
        }

        .country-grid,
        .region-grid,
        .city-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 12px;
        }

        .country-card,
        .region-card,
        .city-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .country-card:hover,
        .region-card:hover,
        .city-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .country-flag {
            font-size: 1.8rem;
            margin-bottom: 8px;
        }

        .country-card h4,
        .region-card h4,
        .city-card h4 {
            margin: 0 0 6px 0;
            color: #ffffff;
            font-size: 1rem;
        }

        .country-stats,
        .region-stats,
        .city-stats {
            margin: 0;
            color: #96ceb4;
            font-size: 0.85rem;
        }

        .atm-count {
            font-weight: bold;
            color: #4ecdc4;
        }

        .breadcrumb {
            margin-bottom: 12px;
            color: #b0b0b0;
            font-size: 0.85rem;
            padding: 8px 0;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            padding-left: 12px;
        }

        .breadcrumb-item {
            display: inline;
        }

        .breadcrumb-link {
            background: none;
            border: none;
            color: #4ecdc4;
            cursor: pointer;
            text-decoration: underline;
            font-size: 0.85rem;
        }

        .breadcrumb-separator {
            margin: 0 6px;
            color: #666;
        }

        .region-card .region-icon,
        .city-card .city-icon {
            font-size: 1.3rem;
            margin-bottom: 6px;
            display: block;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .bitcoiniacs-visual-selector {
                padding: 12px;
            }

            .visual-selector-header {
                margin-bottom: 12px;
            }

            .visual-selector-header h3 {
                font-size: 1.2rem;
            }

            .country-grid,
            .region-grid,
            .city-grid {
                grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
                gap: 8px;
            }

            .country-card,
            .region-card,
            .city-card {
                padding: 12px;
            }

            .visual-selector-tabs {
                flex-wrap: wrap;
                margin-bottom: 12px;
            }

            .selector-tab {
                padding: 6px 12px;
                font-size: 0.85rem;
            }

            .breadcrumb {
                margin-bottom: 10px;
                padding: 6px 0 6px 10px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Visual Selector Height Fix Test</h1>
        
        <div class="test-section">
            <h2>Height Reduction Summary</h2>
            <div class="height-info">
                <strong>Key Changes Made:</strong>
                <ul>
                    <li><strong>Main container padding:</strong> 20px → 15px (25% reduction)</li>
                    <li><strong>Header margin-bottom:</strong> 20px → 15px (25% reduction)</li>
                    <li><strong>Header h3 margin:</strong> 0 0 5px 0 → 0 0 3px 0 (40% reduction)</li>
                    <li><strong>Tab padding:</strong> 10px 20px → 8px 16px (20% reduction)</li>
                    <li><strong>Tabs margin-bottom:</strong> 20px → 15px (25% reduction)</li>
                    <li><strong>Breadcrumb margin-bottom:</strong> 20px → 12px (40% reduction)</li>
                    <li><strong>Card padding:</strong> 20px → 15px (25% reduction)</li>
                    <li><strong>Grid gap:</strong> 15px → 12px (20% reduction)</li>
                    <li><strong>Country flag margin:</strong> 10px → 8px (20% reduction)</li>
                    <li><strong>Card title margin:</strong> 0 0 10px 0 → 0 0 6px 0 (40% reduction)</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Visual Selector Test</h2>
            <p>This shows the updated visual selector with reduced height and improved spacing:</p>
            
            <!-- Include the actual visual selector styles and structure -->
            <div class="bitcoiniacs-visual-selector" id="test-visual-selector">
                <div class="visual-selector-header">
                    <h3>Select Location Visually</h3>
                    <p>Click on a region to explore ATM locations</p>
                </div>
                
                <div class="visual-selector-tabs">
                    <button class="selector-tab active" data-tab="country">Country</button>
                    <button class="selector-tab" data-tab="region">Region</button>
                    <button class="selector-tab" data-tab="city">City</button>
                </div>
                
                <div class="visual-selector-content">
                    <!-- Country Selection -->
                    <div class="selector-panel active" id="country-panel">
                        <div class="country-grid">
                            <div class="country-card" data-country="CA">
                                <div class="country-flag">🇨🇦</div>
                                <h4>Canada</h4>
                                <p class="country-stats">
                                    <span class="atm-count">29</span> ATMs
                                </p>
                            </div>
                            
                            <div class="country-card" data-country="PH">
                                <div class="country-flag">🇵🇭</div>
                                <h4>Philippines</h4>
                                <p class="country-stats">
                                    <span class="atm-count">2</span> ATMs
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Region Selection -->
                    <div class="selector-panel" id="region-panel">
                        <div class="breadcrumb">
                            <span class="breadcrumb-item">
                                <button class="breadcrumb-link" data-tab="country">Countries</button>
                            </span>
                            <span class="breadcrumb-separator">›</span>
                            <span class="breadcrumb-item current-country">Canada</span>
                        </div>
                        
                        <div class="region-grid">
                            <div class="region-card" data-region="AB">
                                <span class="region-icon">🏔️</span>
                                <h4>Alberta</h4>
                                <p class="region-stats">
                                    <span class="atm-count">5</span> ATMs
                                </p>
                            </div>
                            
                            <div class="region-card" data-region="BC">
                                <span class="region-icon">🌲</span>
                                <h4>British Columbia</h4>
                                <p class="region-stats">
                                    <span class="atm-count">18</span> ATMs
                                </p>
                            </div>
                            
                            <div class="region-card" data-region="ON">
                                <span class="region-icon">🏙️</span>
                                <h4>Ontario</h4>
                                <p class="region-stats">
                                    <span class="atm-count">6</span> ATMs
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Responsive Test</h2>
            <p>Test the visual selector at different screen sizes:</p>
            
            <div class="comparison">
                <div class="comparison-item">
                    <h3>Desktop View (1200px+)</h3>
                    <ul>
                        <li>Grid: 3-4 cards per row</li>
                        <li>Full padding and spacing</li>
                        <li>Larger fonts and icons</li>
                    </ul>
                </div>
                
                <div class="comparison-item">
                    <h3>Mobile View (768px-)</h3>
                    <ul>
                        <li>Grid: 2-3 cards per row</li>
                        <li>Reduced padding (12px)</li>
                        <li>Smaller fonts and spacing</li>
                        <li>Compact breadcrumbs</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Height Measurements</h2>
            <div id="height-measurements">
                <p>Measuring visual selector dimensions...</p>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Tab switching functionality
            $('.selector-tab').click(function() {
                const tab = $(this).data('tab');
                
                // Update active tab
                $('.selector-tab').removeClass('active');
                $(this).addClass('active');
                
                // Show corresponding panel
                $('.selector-panel').removeClass('active');
                if (tab === 'region') {
                    $('#region-panel').addClass('active');
                } else {
                    $('#country-panel').addClass('active');
                }
            });
            
            // Country card click
            $('.country-card').click(function() {
                $('.selector-tab[data-tab="region"]').click();
            });
            
            // Breadcrumb navigation
            $('.breadcrumb-link').click(function() {
                const tab = $(this).data('tab');
                $('.selector-tab[data-tab="' + tab + '"]').click();
            });
            
            // Measure heights
            setTimeout(function() {
                const $selector = $('.bitcoiniacs-visual-selector');
                const $header = $('.visual-selector-header');
                const $tabs = $('.visual-selector-tabs');
                const $content = $('.visual-selector-content');
                
                const measurements = `
                    <div class="height-info">
                        <strong>Current Measurements:</strong><br>
                        • Total visual selector height: ${$selector.outerHeight()}px<br>
                        • Header height: ${$header.outerHeight()}px<br>
                        • Tabs height: ${$tabs.outerHeight()}px<br>
                        • Content area height: ${$content.outerHeight()}px<br>
                        • Window width: ${$(window).width()}px
                    </div>
                `;
                
                $('#height-measurements').html(measurements);
            }, 100);
            
            // Update measurements on resize
            $(window).resize(function() {
                setTimeout(function() {
                    const $selector = $('.bitcoiniacs-visual-selector');
                    $('#height-measurements .height-info').html(`
                        <strong>Current Measurements:</strong><br>
                        • Total visual selector height: ${$selector.outerHeight()}px<br>
                        • Window width: ${$(window).width()}px
                    `);
                }, 100);
            });
        });
    </script>
</body>
</html>
