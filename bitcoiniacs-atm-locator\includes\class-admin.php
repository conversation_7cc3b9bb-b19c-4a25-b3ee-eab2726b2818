<?php
/**
 * Admin interface for Bitcoiniacs ATM Locator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class BitcoiniacsATMAdmin {
  
  /**
   * Constructor
   */
  public function __construct() {
    add_action('admin_menu', array($this, 'add_admin_menu'));
    add_action('admin_init', array($this, 'admin_init'));
    add_action('wp_ajax_bitcoiniacs_atm_save_location', array($this, 'ajax_save_location'));
    add_action('wp_ajax_bitcoiniacs_atm_delete_location', array($this, 'ajax_delete_location'));
  }
  
  /**
   * Add admin menu
   */
  public function add_admin_menu() {
    add_menu_page(
      __('ATM Locations', 'bitcoiniacs-atm-locator'),
      __('ATM Locations', 'bitcoiniacs-atm-locator'),
      'manage_options',
      'bitcoiniacs-atm-locations',
      array($this, 'admin_page_locations'),
      'dashicons-location-alt',
      30
    );
    
    add_submenu_page(
      'bitcoiniacs-atm-locations',
      __('All Locations', 'bitcoiniacs-atm-locator'),
      __('All Locations', 'bitcoiniacs-atm-locator'),
      'manage_options',
      'bitcoiniacs-atm-locations',
      array($this, 'admin_page_locations')
    );
    
    add_submenu_page(
      'bitcoiniacs-atm-locations',
      __('Add New Location', 'bitcoiniacs-atm-locator'),
      __('Add New', 'bitcoiniacs-atm-locator'),
      'manage_options',
      'bitcoiniacs-atm-add-location',
      array($this, 'admin_page_add_location')
    );
    
    add_submenu_page(
      'bitcoiniacs-atm-locations',
      __('Settings', 'bitcoiniacs-atm-locator'),
      __('Settings', 'bitcoiniacs-atm-locator'),
      'manage_options',
      'bitcoiniacs-atm-settings',
      array($this, 'admin_page_settings')
    );
  }
  
  /**
   * Admin init
   */
  public function admin_init() {
    // Register settings
    register_setting('bitcoiniacs_atm_settings', 'bitcoiniacs_atm_default_zoom');
    register_setting('bitcoiniacs_atm_settings', 'bitcoiniacs_atm_default_center_lat');
    register_setting('bitcoiniacs_atm_settings', 'bitcoiniacs_atm_default_center_lng');
    register_setting('bitcoiniacs_atm_settings', 'bitcoiniacs_atm_map_style');
  }
  
  /**
   * Admin page - All locations
   */
  public function admin_page_locations() {
    $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
    $location_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if ($action === 'edit' && $location_id) {
      $this->admin_page_edit_location($location_id);
      return;
    }
    
    // Handle bulk actions
    if (isset($_POST['action']) && $_POST['action'] === 'delete' && isset($_POST['locations'])) {
      check_admin_referer('bitcoiniacs_atm_bulk_action');
      $deleted_count = 0;
      foreach ($_POST['locations'] as $id) {
        if (BitcoiniacsATMDatabase::delete_location(intval($id))) {
          $deleted_count++;
        }
      }
      echo '<div class="notice notice-success"><p>' . 
           sprintf(__('%d location(s) deleted successfully.', 'bitcoiniacs-atm-locator'), $deleted_count) . 
           '</p></div>';
    }
    
    $locations = BitcoiniacsATMDatabase::get_all_locations();
    
    ?>
    <div class="wrap">
      <h1 class="wp-heading-inline"><?php _e('ATM Locations', 'bitcoiniacs-atm-locator'); ?></h1>
      <a href="<?php echo admin_url('admin.php?page=bitcoiniacs-atm-add-location'); ?>" class="page-title-action">
        <?php _e('Add New', 'bitcoiniacs-atm-locator'); ?>
      </a>
      <hr class="wp-header-end">
      
      <form method="post">
        <?php wp_nonce_field('bitcoiniacs_atm_bulk_action'); ?>
        <div class="tablenav top">
          <div class="alignleft actions bulkactions">
            <select name="action">
              <option value="-1"><?php _e('Bulk Actions', 'bitcoiniacs-atm-locator'); ?></option>
              <option value="delete"><?php _e('Delete', 'bitcoiniacs-atm-locator'); ?></option>
            </select>
            <input type="submit" class="button action" value="<?php _e('Apply', 'bitcoiniacs-atm-locator'); ?>">
          </div>
        </div>
        
        <table class="wp-list-table widefat fixed striped">
          <thead>
            <tr>
              <td class="manage-column column-cb check-column">
                <input type="checkbox" id="cb-select-all-1">
              </td>
              <th class="manage-column"><?php _e('Location Name', 'bitcoiniacs-atm-locator'); ?></th>
              <th class="manage-column"><?php _e('Address', 'bitcoiniacs-atm-locator'); ?></th>
              <th class="manage-column"><?php _e('Province', 'bitcoiniacs-atm-locator'); ?></th>
              <th class="manage-column"><?php _e('Services', 'bitcoiniacs-atm-locator'); ?></th>
              <th class="manage-column"><?php _e('Status', 'bitcoiniacs-atm-locator'); ?></th>
              <th class="manage-column"><?php _e('Actions', 'bitcoiniacs-atm-locator'); ?></th>
            </tr>
          </thead>
          <tbody>
            <?php if (empty($locations)): ?>
              <tr>
                <td colspan="7"><?php _e('No locations found.', 'bitcoiniacs-atm-locator'); ?></td>
              </tr>
            <?php else: ?>
              <?php foreach ($locations as $location): ?>
                <tr>
                  <th scope="row" class="check-column">
                    <input type="checkbox" name="locations[]" value="<?php echo esc_attr($location->id); ?>">
                  </th>
                  <td>
                    <strong>
                      <a href="<?php echo admin_url('admin.php?page=bitcoiniacs-atm-locations&action=edit&id=' . $location->id); ?>">
                        <?php echo esc_html($location->location_name); ?>
                      </a>
                    </strong>
                  </td>
                  <td><?php echo esc_html($location->address); ?></td>
                  <td><?php echo esc_html($location->province); ?></td>
                  <td>
                    <?php if ($location->can_buy): ?>
                      <span class="dashicons dashicons-yes-alt" title="<?php _e('Can Buy', 'bitcoiniacs-atm-locator'); ?>"></span>
                    <?php endif; ?>
                    <?php if ($location->can_sell): ?>
                      <span class="dashicons dashicons-money-alt" title="<?php _e('Can Sell', 'bitcoiniacs-atm-locator'); ?>"></span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <span class="status-<?php echo strtolower($location->status); ?>">
                      <?php echo esc_html($location->status); ?>
                    </span>
                  </td>
                  <td>
                    <a href="<?php echo admin_url('admin.php?page=bitcoiniacs-atm-locations&action=edit&id=' . $location->id); ?>" class="button button-small">
                      <?php _e('Edit', 'bitcoiniacs-atm-locator'); ?>
                    </a>
                    <button type="button" class="button button-small button-link-delete" onclick="deleteLocation(<?php echo $location->id; ?>)">
                      <?php _e('Delete', 'bitcoiniacs-atm-locator'); ?>
                    </button>
                  </td>
                </tr>
              <?php endforeach; ?>
            <?php endif; ?>
          </tbody>
        </table>
      </form>
    </div>
    
    <script>
    function deleteLocation(id) {
      if (confirm('<?php _e('Are you sure you want to delete this location?', 'bitcoiniacs-atm-locator'); ?>')) {
        jQuery.post(ajaxurl, {
          action: 'bitcoiniacs_atm_delete_location',
          id: id,
          nonce: '<?php echo wp_create_nonce('bitcoiniacs_atm_admin_nonce'); ?>'
        }, function(response) {
          if (response.success) {
            location.reload();
          } else {
            alert('<?php _e('Error deleting location.', 'bitcoiniacs-atm-locator'); ?>');
          }
        });
      }
    }
    
    // Select all checkbox functionality
    jQuery('#cb-select-all-1').on('change', function() {
      jQuery('input[name="locations[]"]').prop('checked', this.checked);
    });
    </script>
    
    <style>
    .status-active { color: #46b450; font-weight: bold; }
    .status-inactive { color: #dc3232; }
    .dashicons { color: #46b450; }
    </style>
    <?php
  }
  
  /**
   * Admin page - Add new location
   */
  public function admin_page_add_location() {
    $this->render_location_form();
  }
  
  /**
   * Admin page - Edit location
   */
  public function admin_page_edit_location($location_id) {
    $location = BitcoiniacsATMDatabase::get_location($location_id);
    if (!$location) {
      echo '<div class="notice notice-error"><p>' . __('Location not found.', 'bitcoiniacs-atm-locator') . '</p></div>';
      return;
    }
    
    $this->render_location_form($location);
  }
  
  /**
   * Render location form
   */
  private function render_location_form($location = null) {
    $is_edit = !empty($location);
    $title = $is_edit ? __('Edit Location', 'bitcoiniacs-atm-locator') : __('Add New Location', 'bitcoiniacs-atm-locator');
    
    ?>
    <div class="wrap">
      <h1><?php echo esc_html($title); ?></h1>
      
      <form id="location-form" method="post">
        <?php wp_nonce_field('bitcoiniacs_atm_save_location'); ?>
        <?php if ($is_edit): ?>
          <input type="hidden" name="location_id" value="<?php echo esc_attr($location->id); ?>">
        <?php endif; ?>
        
        <table class="form-table">
          <tr>
            <th scope="row">
              <label for="location_name"><?php _e('Location Name', 'bitcoiniacs-atm-locator'); ?> *</label>
            </th>
            <td>
              <input type="text" id="location_name" name="location_name" class="regular-text" 
                     value="<?php echo $is_edit ? esc_attr($location->location_name) : ''; ?>" required>
            </td>
          </tr>
          <tr>
            <th scope="row">
              <label for="address"><?php _e('Address', 'bitcoiniacs-atm-locator'); ?> *</label>
            </th>
            <td>
              <textarea id="address" name="address" rows="3" class="large-text" required><?php echo $is_edit ? esc_textarea($location->address) : ''; ?></textarea>
            </td>
          </tr>
          <tr>
            <th scope="row">
              <label for="province"><?php _e('Province/State', 'bitcoiniacs-atm-locator'); ?> *</label>
            </th>
            <td>
              <input type="text" id="province" name="province" class="regular-text" 
                     value="<?php echo $is_edit ? esc_attr($location->province) : ''; ?>" required>
              <p class="description"><?php _e('Use standard abbreviations (e.g., BC, ON, AB)', 'bitcoiniacs-atm-locator'); ?></p>
            </td>
          </tr>
          <tr>
            <th scope="row">
              <label for="country"><?php _e('Country', 'bitcoiniacs-atm-locator'); ?> *</label>
            </th>
            <td>
              <select id="country" name="country" required>
                <option value="CA" <?php echo ($is_edit && $location->country === 'CA') ? 'selected' : ''; ?>>Canada</option>
                <option value="US" <?php echo ($is_edit && $location->country === 'US') ? 'selected' : ''; ?>>United States</option>
                <option value="PH" <?php echo ($is_edit && $location->country === 'PH') ? 'selected' : ''; ?>>Philippines</option>
              </select>
            </td>
          </tr>
          <tr>
            <th scope="row">
              <label for="latitude"><?php _e('Latitude', 'bitcoiniacs-atm-locator'); ?> *</label>
            </th>
            <td>
              <input type="number" id="latitude" name="latitude" step="any" class="regular-text" 
                     value="<?php echo $is_edit ? esc_attr($location->latitude) : ''; ?>" required>
            </td>
          </tr>
          <tr>
            <th scope="row">
              <label for="longitude"><?php _e('Longitude', 'bitcoiniacs-atm-locator'); ?> *</label>
            </th>
            <td>
              <input type="number" id="longitude" name="longitude" step="any" class="regular-text" 
                     value="<?php echo $is_edit ? esc_attr($location->longitude) : ''; ?>" required>
              <button type="button" id="geocode-btn" class="button"><?php _e('Get Coordinates', 'bitcoiniacs-atm-locator'); ?></button>
            </td>
          </tr>
          <tr>
            <th scope="row"><?php _e('Services', 'bitcoiniacs-atm-locator'); ?></th>
            <td>
              <label>
                <input type="checkbox" name="can_buy" value="1" <?php echo ($is_edit && $location->can_buy) ? 'checked' : ''; ?>>
                <?php _e('Can Buy Bitcoin', 'bitcoiniacs-atm-locator'); ?>
              </label><br>
              <label>
                <input type="checkbox" name="can_sell" value="1" <?php echo ($is_edit && $location->can_sell) ? 'checked' : ''; ?>>
                <?php _e('Can Sell Bitcoin', 'bitcoiniacs-atm-locator'); ?>
              </label>
            </td>
          </tr>
          <tr>
            <th scope="row">
              <label for="status"><?php _e('Status', 'bitcoiniacs-atm-locator'); ?></label>
            </th>
            <td>
              <select id="status" name="status">
                <option value="Active" <?php echo ($is_edit && $location->status === 'Active') ? 'selected' : ''; ?>><?php _e('Active', 'bitcoiniacs-atm-locator'); ?></option>
                <option value="Inactive" <?php echo ($is_edit && $location->status === 'Inactive') ? 'selected' : ''; ?>><?php _e('Inactive', 'bitcoiniacs-atm-locator'); ?></option>
                <option value="Maintenance" <?php echo ($is_edit && $location->status === 'Maintenance') ? 'selected' : ''; ?>><?php _e('Maintenance', 'bitcoiniacs-atm-locator'); ?></option>
              </select>
            </td>
          </tr>
          <tr>
            <th scope="row">
              <label for="operating_hours"><?php _e('Operating Hours', 'bitcoiniacs-atm-locator'); ?></label>
            </th>
            <td>
              <textarea id="operating_hours" name="operating_hours" rows="3" class="large-text"><?php echo $is_edit ? esc_textarea($location->operating_hours) : ''; ?></textarea>
            </td>
          </tr>
          <tr>
            <th scope="row">
              <label for="phone"><?php _e('Phone', 'bitcoiniacs-atm-locator'); ?></label>
            </th>
            <td>
              <input type="text" id="phone" name="phone" class="regular-text" 
                     value="<?php echo $is_edit ? esc_attr($location->phone) : ''; ?>">
            </td>
          </tr>
          <tr>
            <th scope="row">
              <label for="website"><?php _e('Website', 'bitcoiniacs-atm-locator'); ?></label>
            </th>
            <td>
              <input type="url" id="website" name="website" class="regular-text" 
                     value="<?php echo $is_edit ? esc_attr($location->website) : ''; ?>">
            </td>
          </tr>
          <tr>
            <th scope="row">
              <label for="notes"><?php _e('Notes', 'bitcoiniacs-atm-locator'); ?></label>
            </th>
            <td>
              <textarea id="notes" name="notes" rows="3" class="large-text"><?php echo $is_edit ? esc_textarea($location->notes) : ''; ?></textarea>
            </td>
          </tr>
        </table>
        
        <p class="submit">
          <input type="submit" class="button-primary" value="<?php echo $is_edit ? __('Update Location', 'bitcoiniacs-atm-locator') : __('Add Location', 'bitcoiniacs-atm-locator'); ?>">
          <a href="<?php echo admin_url('admin.php?page=bitcoiniacs-atm-locations'); ?>" class="button"><?php _e('Cancel', 'bitcoiniacs-atm-locator'); ?></a>
        </p>
      </form>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
      $('#location-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        formData += '&action=bitcoiniacs_atm_save_location';
        
        $.post(ajaxurl, formData, function(response) {
          if (response.success) {
            window.location.href = '<?php echo admin_url('admin.php?page=bitcoiniacs-atm-locations'); ?>';
          } else {
            alert('Error: ' + response.data);
          }
        });
      });
      
      $('#geocode-btn').on('click', function() {
        var address = $('#address').val();
        if (!address) {
          alert('<?php _e('Please enter an address first.', 'bitcoiniacs-atm-locator'); ?>');
          return;
        }
        
        // Simple geocoding using a free service
        var geocodeUrl = 'https://nominatim.openstreetmap.org/search?format=json&q=' + encodeURIComponent(address);
        
        $.get(geocodeUrl, function(data) {
          if (data && data.length > 0) {
            $('#latitude').val(parseFloat(data[0].lat).toFixed(8));
            $('#longitude').val(parseFloat(data[0].lon).toFixed(8));
            alert('<?php _e('Coordinates found and filled in.', 'bitcoiniacs-atm-locator'); ?>');
          } else {
            alert('<?php _e('Could not find coordinates for this address.', 'bitcoiniacs-atm-locator'); ?>');
          }
        }).fail(function() {
          alert('<?php _e('Geocoding service unavailable.', 'bitcoiniacs-atm-locator'); ?>');
        });
      });
    });
    </script>
    <?php
  }
