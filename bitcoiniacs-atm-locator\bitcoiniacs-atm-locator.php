<?php
/**
 * Plugin Name: Bitcoiniacs ATM Locator
 * Plugin URI: https://bitcoiniacs.com
 * Description: A comprehensive Bitcoin ATM locator plugin with admin management and visual map interface for finding Bitcoin ATMs across Canada and internationally.
 * Version: 1.0.0
 * Author: Bitcoiniacs
 * Author URI: https://bitcoiniacs.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: bitcoiniacs-atm-locator
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Define plugin constants
define('BITCOINIACS_ATM_VERSION', '1.0.0');
define('BITCOINIACS_ATM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('BITCOINIACS_ATM_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('BITCOINIACS_ATM_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class BitcoiniacsATMLocator {
  
  /**
   * Single instance of the plugin
   */
  private static $instance = null;
  
  /**
   * Database table name
   */
  public static $table_name;
  
  /**
   * Get single instance
   */
  public static function get_instance() {
    if (null === self::$instance) {
      self::$instance = new self();
    }
    return self::$instance;
  }
  
  /**
   * Constructor
   */
  private function __construct() {
    global $wpdb;
    self::$table_name = $wpdb->prefix . 'bitcoiniacs_atm_locations';
    
    add_action('init', array($this, 'init'));
    add_action('plugins_loaded', array($this, 'load_textdomain'));
    
    // Activation and deactivation hooks
    register_activation_hook(__FILE__, array($this, 'activate'));
    register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    register_uninstall_hook(__FILE__, array('BitcoiniacsATMLocator', 'uninstall'));
  }
  
  /**
   * Initialize plugin
   */
  public function init() {
    // Load required files
    $this->load_dependencies();
    
    // Initialize components
    if (is_admin()) {
      new BitcoiniacsATMAdmin();
    }
    
    new BitcoiniacsATMFrontend();
    new BitcoiniacsATMShortcode();
    
    // Enqueue scripts and styles
    add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
    add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
  }
  
  /**
   * Load plugin dependencies
   */
  private function load_dependencies() {
    require_once BITCOINIACS_ATM_PLUGIN_PATH . 'includes/class-database.php';
    require_once BITCOINIACS_ATM_PLUGIN_PATH . 'includes/class-admin.php';
    require_once BITCOINIACS_ATM_PLUGIN_PATH . 'includes/class-frontend.php';
    require_once BITCOINIACS_ATM_PLUGIN_PATH . 'includes/class-shortcode.php';
    require_once BITCOINIACS_ATM_PLUGIN_PATH . 'includes/class-api.php';
  }
  
  /**
   * Load text domain for translations
   */
  public function load_textdomain() {
    load_plugin_textdomain(
      'bitcoiniacs-atm-locator',
      false,
      dirname(BITCOINIACS_ATM_PLUGIN_BASENAME) . '/languages'
    );
  }
  
  /**
   * Enqueue frontend assets
   */
  public function enqueue_frontend_assets() {
    wp_enqueue_style(
      'bitcoiniacs-atm-frontend',
      BITCOINIACS_ATM_PLUGIN_URL . 'assets/css/frontend.css',
      array(),
      BITCOINIACS_ATM_VERSION
    );
    
    wp_enqueue_script(
      'leaflet-js',
      'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.js',
      array(),
      '1.9.4',
      true
    );
    
    wp_enqueue_style(
      'leaflet-css',
      'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.css',
      array(),
      '1.9.4'
    );
    
    wp_enqueue_script(
      'bitcoiniacs-atm-frontend',
      BITCOINIACS_ATM_PLUGIN_URL . 'assets/js/frontend.js',
      array('jquery', 'leaflet-js'),
      BITCOINIACS_ATM_VERSION,
      true
    );
    
    // Localize script with AJAX URL and nonce
    wp_localize_script('bitcoiniacs-atm-frontend', 'bitcoiniacs_atm_ajax', array(
      'ajax_url' => admin_url('admin-ajax.php'),
      'nonce' => wp_create_nonce('bitcoiniacs_atm_nonce'),
      'plugin_url' => BITCOINIACS_ATM_PLUGIN_URL
    ));
  }
  
  /**
   * Enqueue admin assets
   */
  public function enqueue_admin_assets($hook) {
    // Only load on our admin pages
    if (strpos($hook, 'bitcoiniacs-atm') === false) {
      return;
    }
    
    wp_enqueue_style(
      'bitcoiniacs-atm-admin',
      BITCOINIACS_ATM_PLUGIN_URL . 'assets/css/admin.css',
      array(),
      BITCOINIACS_ATM_VERSION
    );
    
    wp_enqueue_script(
      'bitcoiniacs-atm-admin',
      BITCOINIACS_ATM_PLUGIN_URL . 'assets/js/admin.js',
      array('jquery'),
      BITCOINIACS_ATM_VERSION,
      true
    );
    
    wp_localize_script('bitcoiniacs-atm-admin', 'bitcoiniacs_atm_admin', array(
      'ajax_url' => admin_url('admin-ajax.php'),
      'nonce' => wp_create_nonce('bitcoiniacs_atm_admin_nonce')
    ));
  }
  
  /**
   * Plugin activation
   */
  public function activate() {
    BitcoiniacsATMDatabase::create_tables();
    BitcoiniacsATMDatabase::insert_seed_data();
    
    // Create upload directory for any future file uploads
    $upload_dir = wp_upload_dir();
    $plugin_upload_dir = $upload_dir['basedir'] . '/bitcoiniacs-atm-locator';
    if (!file_exists($plugin_upload_dir)) {
      wp_mkdir_p($plugin_upload_dir);
    }
    
    // Set default options
    add_option('bitcoiniacs_atm_version', BITCOINIACS_ATM_VERSION);
    add_option('bitcoiniacs_atm_default_zoom', 4);
    add_option('bitcoiniacs_atm_default_center_lat', 53.7267);
    add_option('bitcoiniacs_atm_default_center_lng', -127.6476);
    
    // Flush rewrite rules
    flush_rewrite_rules();
  }
  
  /**
   * Plugin deactivation
   */
  public function deactivate() {
    // Clean up any temporary data
    flush_rewrite_rules();
  }
  
  /**
   * Plugin uninstallation
   */
  public static function uninstall() {
    // Remove database tables
    BitcoiniacsATMDatabase::drop_tables();
    
    // Remove options
    delete_option('bitcoiniacs_atm_version');
    delete_option('bitcoiniacs_atm_default_zoom');
    delete_option('bitcoiniacs_atm_default_center_lat');
    delete_option('bitcoiniacs_atm_default_center_lng');
    
    // Remove upload directory
    $upload_dir = wp_upload_dir();
    $plugin_upload_dir = $upload_dir['basedir'] . '/bitcoiniacs-atm-locator';
    if (file_exists($plugin_upload_dir)) {
      array_map('unlink', glob("$plugin_upload_dir/*"));
      rmdir($plugin_upload_dir);
    }
  }
}

// Initialize the plugin
BitcoiniacsATMLocator::get_instance();
